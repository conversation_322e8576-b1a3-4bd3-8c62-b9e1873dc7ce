// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		09003D662E2DE92A005C14F7 /* KeyboardShortcuts in Frameworks */ = {isa = PBXBuildFile; productRef = 09003D652E2DE92A005C14F7 /* KeyboardShortcuts */; };
		635363151D0FF2B00094EFCE /* Defaults.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635363141D0FF2B00094EFCE /* Defaults.swift */; };
		638E33F41CFEB31900069198 /* ShortcutTableCellView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 638E33F31CFEB31900069198 /* ShortcutTableCellView.swift */; };
		63AFC3521D89452C00F65803 /* NSObject+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63AFC3511D89452C00F65803 /* NSObject+Extension.swift */; };
		B2B04B781CC74DC7005C9BEF /* MainWindowController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2B04B771CC74DC7005C9BEF /* MainWindowController.swift */; };
		B2D65A071CDAF2D500E39A65 /* NSApplication+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2D65A061CDAF2D500E39A65 /* NSApplication+Extension.swift */; };
		B2E1B0441CC48CD900FB687F /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2E1B0431CC48CD900FB687F /* AppDelegate.swift */; };
		B2E1B0481CC48CD900FB687F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = B2E1B0471CC48CD900FB687F /* Assets.xcassets */; };
		B2E1B04B1CC48CD900FB687F /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B2E1B0491CC48CD900FB687F /* Main.storyboard */; };
		B2E1B0531CC4A81D00FB687F /* AppModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2E1B0521CC4A81D00FB687F /* AppModel.swift */; };
		B2E1B0571CC4A85700FB687F /* AppsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2E1B0561CC4A85700FB687F /* AppsManager.swift */; };
		B2E1B05B1CC4AE2600FB687F /* Constant.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2E1B05A1CC4AE2600FB687F /* Constant.swift */; };
		B2E1B05D1CC4AE4500FB687F /* DefaultsKeys+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2E1B05C1CC4AE4500FB687F /* DefaultsKeys+Extension.swift */; };
		B2E1B0691CC4CDD100FB687F /* TOLWindowController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2E1B0681CC4CDD100FB687F /* TOLWindowController.swift */; };
		B2E1B06B1CC4D49800FB687F /* SettingsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2E1B06A1CC4D49800FB687F /* SettingsViewController.swift */; };
		B2F635FC1CD35BDD00E1232D /* String+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2F635FB1CD35BDD00E1232D /* String+Extension.swift */; };
		BE503F462B49997900D753BB /* DateExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE503F452B49997900D753BB /* DateExtensions.swift */; };
		BE6B4B5527D511020080F323 /* LaunchAtLogin in Frameworks */ = {isa = PBXBuildFile; productRef = BE6B4B5427D511020080F323 /* LaunchAtLogin */; };
		E14196061D9F5A7D00ABBD8C /* zh-Hans.lproj in Resources */ = {isa = PBXBuildFile; fileRef = E14196051D9F5A7D00ABBD8C /* zh-Hans.lproj */; };
		E166F32B1CE4B7E300F7C7B8 /* StatusItemController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E166F32A1CE4B7E300F7C7B8 /* StatusItemController.swift */; };
		E166F3391CE7801A00F7C7B8 /* ShortcutListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E166F3371CE7801A00F7C7B8 /* ShortcutListViewController.swift */; };
		E166F3401CE781EC00F7C7B8 /* ShortcutMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = E166F33F1CE781EC00F7C7B8 /* ShortcutMonitor.swift */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		E1F735D52479679300E3D85E /* Copy Files */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = Contents/Library/LoginItems;
			dstSubfolderSpec = 1;
			files = (
			);
			name = "Copy Files";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		635363141D0FF2B00094EFCE /* Defaults.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Defaults.swift; sourceTree = "<group>"; };
		638E33F31CFEB31900069198 /* ShortcutTableCellView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ShortcutTableCellView.swift; sourceTree = "<group>"; };
		63AFC3511D89452C00F65803 /* NSObject+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "NSObject+Extension.swift"; sourceTree = "<group>"; };
		B2B04B771CC74DC7005C9BEF /* MainWindowController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MainWindowController.swift; sourceTree = "<group>"; };
		B2D65A061CDAF2D500E39A65 /* NSApplication+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "NSApplication+Extension.swift"; sourceTree = "<group>"; };
		B2E1B0401CC48CD900FB687F /* Switchman.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Switchman.app; sourceTree = BUILT_PRODUCTS_DIR; };
		B2E1B0431CC48CD900FB687F /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		B2E1B0471CC48CD900FB687F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		B2E1B04A1CC48CD900FB687F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		B2E1B04C1CC48CD900FB687F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B2E1B0521CC4A81D00FB687F /* AppModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppModel.swift; sourceTree = "<group>"; };
		B2E1B0561CC4A85700FB687F /* AppsManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppsManager.swift; sourceTree = "<group>"; };
		B2E1B05A1CC4AE2600FB687F /* Constant.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Constant.swift; sourceTree = "<group>"; };
		B2E1B05C1CC4AE4500FB687F /* DefaultsKeys+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "DefaultsKeys+Extension.swift"; sourceTree = "<group>"; };
		B2E1B0681CC4CDD100FB687F /* TOLWindowController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TOLWindowController.swift; sourceTree = "<group>"; };
		B2E1B06A1CC4D49800FB687F /* SettingsViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingsViewController.swift; sourceTree = "<group>"; };
		B2F635FB1CD35BDD00E1232D /* String+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "String+Extension.swift"; sourceTree = "<group>"; };
		BE503F452B49997900D753BB /* DateExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DateExtensions.swift; sourceTree = "<group>"; };
		E1002A341D9F51C000C21721 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Main.strings"; sourceTree = "<group>"; };
		E14196051D9F5A7D00ABBD8C /* zh-Hans.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = "zh-Hans.lproj"; sourceTree = "<group>"; };
		E166F32A1CE4B7E300F7C7B8 /* StatusItemController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = StatusItemController.swift; sourceTree = "<group>"; };
		E166F3371CE7801A00F7C7B8 /* ShortcutListViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ShortcutListViewController.swift; sourceTree = "<group>"; };
		E166F33F1CE781EC00F7C7B8 /* ShortcutMonitor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ShortcutMonitor.swift; sourceTree = "<group>"; };
		E1ED9837212B258D008FCB7D /* Switchman.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Switchman.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B2E1B03D1CC48CD900FB687F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BE6B4B5527D511020080F323 /* LaunchAtLogin in Frameworks */,
				09003D662E2DE92A005C14F7 /* KeyboardShortcuts in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		638E33F21CFEB2E200069198 /* View */ = {
			isa = PBXGroup;
			children = (
				638E33F31CFEB31900069198 /* ShortcutTableCellView.swift */,
			);
			name = View;
			sourceTree = "<group>";
		};
		B2D65A0A1CDB2CAF00E39A65 /* Controller */ = {
			isa = PBXGroup;
			children = (
				E166F32A1CE4B7E300F7C7B8 /* StatusItemController.swift */,
				B2E1B0681CC4CDD100FB687F /* TOLWindowController.swift */,
				B2B04B771CC74DC7005C9BEF /* MainWindowController.swift */,
				E166F3371CE7801A00F7C7B8 /* ShortcutListViewController.swift */,
				B2E1B06A1CC4D49800FB687F /* SettingsViewController.swift */,
			);
			name = Controller;
			sourceTree = "<group>";
		};
		B2E1B0371CC48CD900FB687F = {
			isa = PBXGroup;
			children = (
				B2E1B0421CC48CD900FB687F /* Switchman */,
				B2E1B0411CC48CD900FB687F /* Products */,
			);
			sourceTree = "<group>";
		};
		B2E1B0411CC48CD900FB687F /* Products */ = {
			isa = PBXGroup;
			children = (
				B2E1B0401CC48CD900FB687F /* Switchman.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B2E1B0421CC48CD900FB687F /* Switchman */ = {
			isa = PBXGroup;
			children = (
				B2E1B0431CC48CD900FB687F /* AppDelegate.swift */,
				B2E1B05A1CC4AE2600FB687F /* Constant.swift */,
				638E33F21CFEB2E200069198 /* View */,
				B2D65A0A1CDB2CAF00E39A65 /* Controller */,
				B2E1B0621CC4B16600FB687F /* Extension */,
				B2E1B0551CC4A84B00FB687F /* Helper */,
				B2E1B0541CC4A82300FB687F /* Model */,
				B2E1B0471CC48CD900FB687F /* Assets.xcassets */,
				B2E1B0491CC48CD900FB687F /* Main.storyboard */,
				E1ED9838212B25AD008FCB7D /* Supporting Files */,
			);
			path = Switchman;
			sourceTree = "<group>";
		};
		B2E1B0541CC4A82300FB687F /* Model */ = {
			isa = PBXGroup;
			children = (
				B2E1B0521CC4A81D00FB687F /* AppModel.swift */,
			);
			name = Model;
			sourceTree = "<group>";
		};
		B2E1B0551CC4A84B00FB687F /* Helper */ = {
			isa = PBXGroup;
			children = (
				B2E1B0561CC4A85700FB687F /* AppsManager.swift */,
				E166F33F1CE781EC00F7C7B8 /* ShortcutMonitor.swift */,
				635363141D0FF2B00094EFCE /* Defaults.swift */,
			);
			name = Helper;
			sourceTree = "<group>";
		};
		B2E1B0621CC4B16600FB687F /* Extension */ = {
			isa = PBXGroup;
			children = (
				B2E1B05C1CC4AE4500FB687F /* DefaultsKeys+Extension.swift */,
				B2F635FB1CD35BDD00E1232D /* String+Extension.swift */,
				B2D65A061CDAF2D500E39A65 /* NSApplication+Extension.swift */,
				63AFC3511D89452C00F65803 /* NSObject+Extension.swift */,
				BE503F452B49997900D753BB /* DateExtensions.swift */,
			);
			name = Extension;
			sourceTree = "<group>";
		};
		E1ED9838212B25AD008FCB7D /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				B2E1B04C1CC48CD900FB687F /* Info.plist */,
				E1ED9837212B258D008FCB7D /* Switchman.entitlements */,
				E14196051D9F5A7D00ABBD8C /* zh-Hans.lproj */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B2E1B03F1CC48CD900FB687F /* Switchman */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B2E1B04F1CC48CD900FB687F /* Build configuration list for PBXNativeTarget "Switchman" */;
			buildPhases = (
				B2E1B03C1CC48CD900FB687F /* Sources */,
				B2E1B03D1CC48CD900FB687F /* Frameworks */,
				B2E1B03E1CC48CD900FB687F /* Resources */,
				E13F5A3A245FDCD300E923F6 /* SwiftLint */,
				E1F735D52479679300E3D85E /* Copy Files */,
				BE6B4B5627D511110080F323 /* LaunchAtLogin helper */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Switchman;
			packageProductDependencies = (
				BE6B4B5427D511020080F323 /* LaunchAtLogin */,
				09003D652E2DE92A005C14F7 /* KeyboardShortcuts */,
			);
			productName = Thor;
			productReference = B2E1B0401CC48CD900FB687F /* Switchman.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B2E1B0381CC48CD900FB687F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1150;
				LastUpgradeCheck = 1410;
				ORGANIZATIONNAME = gewill.org;
				TargetAttributes = {
					B2E1B03F1CC48CD900FB687F = {
						CreatedOnToolsVersion = 7.3;
						DevelopmentTeamName = "Canfeng Zhu";
						LastSwiftMigration = 1020;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.Sandbox = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = B2E1B03B1CC48CD900FB687F /* Build configuration list for PBXProject "Switchman" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = B2E1B0371CC48CD900FB687F;
			packageReferences = (
				BE6B4B5327D511020080F323 /* XCRemoteSwiftPackageReference "LaunchAtLogin-Legacy" */,
				09003D642E2DE92A005C14F7 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */,
			);
			productRefGroup = B2E1B0411CC48CD900FB687F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B2E1B03F1CC48CD900FB687F /* Switchman */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B2E1B03E1CC48CD900FB687F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B2E1B0481CC48CD900FB687F /* Assets.xcassets in Resources */,
				B2E1B04B1CC48CD900FB687F /* Main.storyboard in Resources */,
				E14196061D9F5A7D00ABBD8C /* zh-Hans.lproj in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		BE6B4B5627D511110080F323 /* LaunchAtLogin helper */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "LaunchAtLogin helper";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${BUILT_PRODUCTS_DIR}/LaunchAtLogin_LaunchAtLogin.bundle/Contents/Resources/copy-helper-swiftpm.sh\"\n";
		};
		E13F5A3A245FDCD300E923F6 /* SwiftLint */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = SwiftLint;
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [[ \"$(uname -m)\" == arm64 ]]; then\n    export PATH=\"/opt/homebrew/bin:$PATH\"\nfi\n\nif which swiftlint > /dev/null; then\n  swiftlint\nelse\n  echo \"warning: SwiftLint not installed, download from https://github.com/realm/SwiftLint\"\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B2E1B03C1CC48CD900FB687F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B2E1B06B1CC4D49800FB687F /* SettingsViewController.swift in Sources */,
				BE503F462B49997900D753BB /* DateExtensions.swift in Sources */,
				B2F635FC1CD35BDD00E1232D /* String+Extension.swift in Sources */,
				B2E1B0531CC4A81D00FB687F /* AppModel.swift in Sources */,
				63AFC3521D89452C00F65803 /* NSObject+Extension.swift in Sources */,
				E166F3391CE7801A00F7C7B8 /* ShortcutListViewController.swift in Sources */,
				635363151D0FF2B00094EFCE /* Defaults.swift in Sources */,
				B2E1B0691CC4CDD100FB687F /* TOLWindowController.swift in Sources */,
				638E33F41CFEB31900069198 /* ShortcutTableCellView.swift in Sources */,
				B2E1B05B1CC4AE2600FB687F /* Constant.swift in Sources */,
				B2E1B05D1CC4AE4500FB687F /* DefaultsKeys+Extension.swift in Sources */,
				B2B04B781CC74DC7005C9BEF /* MainWindowController.swift in Sources */,
				B2E1B0441CC48CD900FB687F /* AppDelegate.swift in Sources */,
				B2E1B0571CC4A85700FB687F /* AppsManager.swift in Sources */,
				E166F32B1CE4B7E300F7C7B8 /* StatusItemController.swift in Sources */,
				E166F3401CE781EC00F7C7B8 /* ShortcutMonitor.swift in Sources */,
				B2D65A071CDAF2D500E39A65 /* NSApplication+Extension.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		B2E1B0491CC48CD900FB687F /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				B2E1B04A1CC48CD900FB687F /* Base */,
				E1002A341D9F51C000C21721 /* zh-Hans */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		B2E1B04D1CC48CD900FB687F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		B2E1B04E1CC48CD900FB687F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = macosx;
			};
			name = Release;
		};
		B2E1B0501CC48CD900FB687F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIconDark;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Switchman/Switchman.entitlements;
				CODE_SIGN_IDENTITY = "Mac Developer";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = RLK76T8Y89;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = Switchman/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Switchman;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = org.gewill.Switchman;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		B2E1B0511CC48CD900FB687F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIconDark;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Switchman/Switchman.entitlements;
				CODE_SIGN_IDENTITY = "Mac Developer";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = RLK76T8Y89;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = Switchman/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Switchman;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = org.gewill.Switchman;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B2E1B03B1CC48CD900FB687F /* Build configuration list for PBXProject "Switchman" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B2E1B04D1CC48CD900FB687F /* Debug */,
				B2E1B04E1CC48CD900FB687F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B2E1B04F1CC48CD900FB687F /* Build configuration list for PBXNativeTarget "Switchman" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B2E1B0501CC48CD900FB687F /* Debug */,
				B2E1B0511CC48CD900FB687F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		09003D642E2DE92A005C14F7 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sindresorhus/KeyboardShortcuts";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.3.0;
			};
		};
		BE6B4B5327D511020080F323 /* XCRemoteSwiftPackageReference "LaunchAtLogin-Legacy" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sindresorhus/LaunchAtLogin-Legacy";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		09003D652E2DE92A005C14F7 /* KeyboardShortcuts */ = {
			isa = XCSwiftPackageProductDependency;
			package = 09003D642E2DE92A005C14F7 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */;
			productName = KeyboardShortcuts;
		};
		BE6B4B5427D511020080F323 /* LaunchAtLogin */ = {
			isa = XCSwiftPackageProductDependency;
			package = BE6B4B5327D511020080F323 /* XCRemoteSwiftPackageReference "LaunchAtLogin-Legacy" */;
			productName = LaunchAtLogin;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = B2E1B0381CC48CD900FB687F /* Project object */;
}
