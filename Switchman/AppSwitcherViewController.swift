//
//  AppSwitcherViewController.swift
//  Switchman
//
//  Created by Augment Agent on 7/24/25.
//  Copyright © 2025 AlvinZhu. All rights reserved.
//

import Cocoa
import KeyboardShortcuts

// MARK: - Layout Mode Enum

enum AppSwitcherLayoutMode: Int, CaseIterable {
    case singleRow = 0
    case multiRow = 1

    var displayName: String {
        switch self {
        case .singleRow:
            return "Single Row"
        case .multiRow:
            return "Multi Row"
        }
    }
}

// MARK: - Custom Collection View Layout

class AppSwitcherFlowLayout: NSCollectionViewFlowLayout {
    var layoutMode: AppSwitcherLayoutMode = .multiRow

    override func prepare() {
        super.prepare()

        guard let collectionView = collectionView else { return }

        let availableWidth = collectionView.bounds.width - sectionInset.left - sectionInset.right
        let itemWidth: CGFloat = 120
        let itemHeight: CGFloat = 110
        let spacing: CGFloat = 20

        itemSize = NSSize(width: itemWidth, height: itemHeight)
        minimumInteritemSpacing = spacing
        minimumLineSpacing = spacing

        switch layoutMode {
        case .singleRow:
            // Single row: horizontal scrolling
            scrollDirection = .horizontal
            // Calculate how many items can fit in available height
            let availableHeight = collectionView.bounds.height - sectionInset.top - sectionInset.bottom
            if availableHeight < itemHeight {
                // If not enough height, adjust item size
                let adjustedHeight = max(80, availableHeight)
                let adjustedWidth = itemWidth * (adjustedHeight / itemHeight)
                itemSize = NSSize(width: adjustedWidth, height: adjustedHeight)
            }

        case .multiRow:
            // Multi row: vertical scrolling
            scrollDirection = .vertical
            // Calculate how many items can fit per row
            let itemsPerRow = max(1, Int((availableWidth + spacing) / (itemWidth + spacing)))
            let totalItemWidth = CGFloat(itemsPerRow) * itemWidth + CGFloat(itemsPerRow - 1) * spacing
            let leftRightPadding = (availableWidth - totalItemWidth) / 2
            sectionInset = NSEdgeInsets(
                top: sectionInset.top,
                left: max(sectionInset.left, leftRightPadding),
                bottom: sectionInset.bottom,
                right: max(sectionInset.right, leftRightPadding)
            )
        }
    }
}

class AppSwitcherViewController: NSViewController {

    // MARK: - Properties

    private var collectionView: NSCollectionView!
    private var scrollView: NSScrollView!
    private var backgroundView: NSVisualEffectView!
    private var customLayout: AppSwitcherFlowLayout!

    private var apps: [AppModel] = []
    private var selectedIndex: Int = 0
    private var layoutMode: AppSwitcherLayoutMode {
        return AppSwitcherLayoutMode(rawValue: defaults[.appSwitcherLayoutMode]) ?? .multiRow
    }

    // Callbacks
    var onClose: (() -> Void)?
    var onAppSelected: ((AppModel) -> Void)?
    
    // MARK: - Lifecycle
    
    override func loadView() {
        // Calculate dynamic window size based on layout mode and screen size
        let screenSize = NSScreen.main?.visibleFrame.size ?? NSSize(width: 1920, height: 1080)
        let windowSize = calculateOptimalWindowSize(for: screenSize)

        view = NSView(frame: NSRect(origin: .zero, size: windowSize))
        setupUI()
    }

    private func calculateOptimalWindowSize(for screenSize: NSSize) -> NSSize {
        let margin: CGFloat = 60 // 30px margin on each side
        let maxWidth = screenSize.width - margin
        let maxHeight = screenSize.height - margin

        let itemWidth: CGFloat = 120
        let itemHeight: CGFloat = 110
        let spacing: CGFloat = 20
        let titleHeight: CGFloat = 80 // Space for title and settings button

        switch layoutMode {
        case .singleRow:
            // Single row: width depends on number of apps, height is fixed
            let totalItemsWidth = CGFloat(apps.count) * itemWidth + CGFloat(max(0, apps.count - 1)) * spacing
            let contentWidth = totalItemsWidth + 40 // 20px padding on each side
            let width = min(maxWidth, max(400, contentWidth))
            let height = min(maxHeight, itemHeight + titleHeight + 40) // 20px padding top/bottom
            return NSSize(width: width, height: height)

        case .multiRow:
            // Multi row: calculate based on optimal grid layout
            let itemsPerRow = max(1, Int((maxWidth - 40) / (itemWidth + spacing))) // 40px total horizontal padding
            let actualItemsPerRow = min(itemsPerRow, apps.count)
            let rows = apps.isEmpty ? 1 : Int(ceil(Double(apps.count) / Double(actualItemsPerRow)))

            let contentWidth = CGFloat(actualItemsPerRow) * itemWidth + CGFloat(max(0, actualItemsPerRow - 1)) * spacing + 40
            let contentHeight = CGFloat(rows) * itemHeight + CGFloat(max(0, rows - 1)) * spacing + titleHeight + 40

            let width = min(maxWidth, max(400, contentWidth))
            let height = min(maxHeight, max(200, contentHeight))
            return NSSize(width: width, height: height)
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        refreshApps()
    }
    
    override func viewDidAppear() {
        super.viewDidAppear()
        view.window?.makeFirstResponder(self)
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        // Background with blur effect
        backgroundView = NSVisualEffectView(frame: view.bounds)
        backgroundView.material = .hudWindow
        backgroundView.blendingMode = .behindWindow
        backgroundView.state = .active
        backgroundView.wantsLayer = true
        backgroundView.layer?.cornerRadius = 12
        backgroundView.autoresizingMask = [.width, .height]
        view.addSubview(backgroundView)

        // Collection view setup with custom layout
        customLayout = AppSwitcherFlowLayout()
        customLayout.layoutMode = layoutMode
        customLayout.itemSize = NSSize(width: 120, height: 110)
        customLayout.minimumInteritemSpacing = 20
        customLayout.minimumLineSpacing = 20
        customLayout.sectionInset = NSEdgeInsets(top: 20, left: 20, bottom: 20, right: 20)

        collectionView = NSCollectionView()
        collectionView.collectionViewLayout = customLayout
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.isSelectable = true
        collectionView.allowsMultipleSelection = false
        collectionView.backgroundColors = [NSColor.clear]

        // Register the cell
        collectionView.register(AppSwitcherCollectionViewItem.self,
                               forItemWithIdentifier: NSUserInterfaceItemIdentifier("AppSwitcherCell"))

        // Scroll view setup based on layout mode
        scrollView = NSScrollView(frame: view.bounds.insetBy(dx: 20, dy: 20))
        scrollView.documentView = collectionView
        scrollView.autohidesScrollers = true
        scrollView.backgroundColor = NSColor.clear
        scrollView.autoresizingMask = [.width, .height]

        // Configure scrolling based on layout mode
        switch layoutMode {
        case .singleRow:
            scrollView.hasVerticalScroller = false
            scrollView.hasHorizontalScroller = true
        case .multiRow:
            scrollView.hasVerticalScroller = true
            scrollView.hasHorizontalScroller = false
        }

        view.addSubview(scrollView)
        
        // Add title label
        let titleLabel = NSTextField(labelWithString: "App Switcher")
        titleLabel.font = NSFont.systemFont(ofSize: 18, weight: .medium)
        titleLabel.textColor = NSColor.labelColor
        titleLabel.alignment = .center
        titleLabel.frame = NSRect(x: 0, y: view.bounds.height - 50, width: view.bounds.width, height: 30)
        titleLabel.autoresizingMask = [.width, .minYMargin]
        view.addSubview(titleLabel)

        // Add settings button
        let settingsButton = NSButton(frame: NSRect(x: view.bounds.width - 50, y: view.bounds.height - 50, width: 30, height: 30))
        settingsButton.title = ""
        settingsButton.bezelStyle = .circular
        settingsButton.image = NSImage(systemSymbolName: "gearshape", accessibilityDescription: "Settings")
        settingsButton.target = self
        settingsButton.action = #selector(openSettings)
        settingsButton.autoresizingMask = [.minXMargin, .minYMargin]
        view.addSubview(settingsButton)

        // Adjust scroll view frame to account for title
        scrollView.frame = NSRect(x: 20, y: 20, width: view.bounds.width - 40, height: view.bounds.height - 80)
    }
    
    // MARK: - Data Management
    
    func refreshApps() {
        apps = AppsManager.manager.selectedApps

        // Update layout mode
        customLayout?.layoutMode = layoutMode

        // Recalculate window size if needed
        if let window = view.window {
            let screenSize = NSScreen.main?.visibleFrame.size ?? NSSize(width: 1920, height: 1080)
            let newSize = calculateOptimalWindowSize(for: screenSize)

            // Only resize if size changed significantly
            let currentSize = window.frame.size
            if abs(newSize.width - currentSize.width) > 10 || abs(newSize.height - currentSize.height) > 10 {
                var newFrame = window.frame
                newFrame.size = newSize
                // Keep window centered
                newFrame.origin.x = (screenSize.width - newSize.width) / 2
                newFrame.origin.y = (screenSize.height - newSize.height) / 2
                window.setFrame(newFrame, display: true, animate: true)
            }
        }

        // Update scroll view configuration
        switch layoutMode {
        case .singleRow:
            scrollView.hasVerticalScroller = false
            scrollView.hasHorizontalScroller = true
        case .multiRow:
            scrollView.hasVerticalScroller = true
            scrollView.hasHorizontalScroller = false
        }

        collectionView?.reloadData()

        // Select first item if available
        if !apps.isEmpty {
            selectedIndex = 0
            selectItem(at: selectedIndex)
        }
    }
    
    private func selectItem(at index: Int) {
        guard index >= 0 && index < apps.count else { return }

        selectedIndex = index
        let indexPath = IndexPath(item: index, section: 0)

        // Clear previous selection
        collectionView.deselectAll(nil)

        // Select the new item
        collectionView.selectItems(at: Set([indexPath]), scrollPosition: .nearestHorizontalEdge)
    }
    
    // MARK: - Keyboard Handling
    
    override func keyDown(with event: NSEvent) {
        let keyCode = event.keyCode
        
        switch keyCode {
        case 36, 76: // Return or Enter
            if selectedIndex < apps.count {
                onAppSelected?(apps[selectedIndex])
            }
            
        case 53: // Escape
            onClose?()
            
        case 125: // Down arrow
            handleDownArrow()

        case 126: // Up arrow
            handleUpArrow()

        case 123: // Left arrow
            handleLeftArrow()

        case 124: // Right arrow
            handleRightArrow()
            
        default:
            super.keyDown(with: event)
        }
    }
    
    override var acceptsFirstResponder: Bool {
        return true
    }

    // MARK: - Keyboard Navigation Helpers

    private func handleDownArrow() {
        switch layoutMode {
        case .singleRow:
            // In single row, down arrow does nothing or cycles to next item
            if selectedIndex < apps.count - 1 {
                selectItem(at: selectedIndex + 1)
            }
        case .multiRow:
            // In multi row, move to item below
            let itemsPerRow = calculateItemsPerRow()
            let newIndex = min(apps.count - 1, selectedIndex + itemsPerRow)
            selectItem(at: newIndex)
        }
    }

    private func handleUpArrow() {
        switch layoutMode {
        case .singleRow:
            // In single row, up arrow does nothing or cycles to previous item
            if selectedIndex > 0 {
                selectItem(at: selectedIndex - 1)
            }
        case .multiRow:
            // In multi row, move to item above
            let itemsPerRow = calculateItemsPerRow()
            let newIndex = max(0, selectedIndex - itemsPerRow)
            selectItem(at: newIndex)
        }
    }

    private func handleLeftArrow() {
        switch layoutMode {
        case .singleRow:
            // In single row, move to previous item
            if selectedIndex > 0 {
                selectItem(at: selectedIndex - 1)
            }
        case .multiRow:
            // In multi row, move to previous item
            if selectedIndex > 0 {
                selectItem(at: selectedIndex - 1)
            }
        }
    }

    private func handleRightArrow() {
        switch layoutMode {
        case .singleRow:
            // In single row, move to next item
            if selectedIndex < apps.count - 1 {
                selectItem(at: selectedIndex + 1)
            }
        case .multiRow:
            // In multi row, move to next item
            if selectedIndex < apps.count - 1 {
                selectItem(at: selectedIndex + 1)
            }
        }
    }

    private func calculateItemsPerRow() -> Int {
        let availableWidth = scrollView.bounds.width - 40 // Account for padding
        let itemWidth: CGFloat = 120
        let spacing: CGFloat = 20
        return max(1, Int((availableWidth + spacing) / (itemWidth + spacing)))
    }

    // MARK: - Actions

    @objc private func openSettings() {
        // Close the App Switcher
        onClose?()

        // Open the main settings window
        sharedAppDelegate?.showMainWindow()
    }

}

// MARK: - NSCollectionViewDataSource

extension AppSwitcherViewController: NSCollectionViewDataSource {
    
    func collectionView(_ collectionView: NSCollectionView, numberOfItemsInSection section: Int) -> Int {
        return apps.count
    }
    
    func collectionView(_ collectionView: NSCollectionView, itemForRepresentedObjectAt indexPath: IndexPath) -> NSCollectionViewItem {
        let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("AppSwitcherCell"), 
                                          for: indexPath) as! AppSwitcherCollectionViewItem
        
        let app = apps[indexPath.item]
        item.configure(with: app)
        
        return item
    }
}

// MARK: - NSCollectionViewDelegate

extension AppSwitcherViewController: NSCollectionViewDelegate {
    
    func collectionView(_ collectionView: NSCollectionView, didSelectItemsAt indexPaths: Set<IndexPath>) {
        if let indexPath = indexPaths.first {
            selectedIndex = indexPath.item
            onAppSelected?(apps[selectedIndex])
        }
    }
}

// MARK: - AppSwitcherCollectionViewItem

class AppSwitcherCollectionViewItem: NSCollectionViewItem {

    private var iconImageView: NSImageView!
    private var nameLabel: NSTextField!
    private var shortcutLabel: NSTextField!
    private var containerView: NSView!

    override func loadView() {
        view = NSView(frame: NSRect(x: 0, y: 0, width: 120, height: 110))
        setupUI()
    }

    private func setupUI() {
        // Container view with background
        containerView = NSView()
        containerView.wantsLayer = true
        containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.withAlphaComponent(0.3).cgColor
        containerView.layer?.cornerRadius = 8
        containerView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(containerView)

        // App name (moved to top)
        nameLabel = NSTextField(labelWithString: "")
        nameLabel.font = NSFont.systemFont(ofSize: 12, weight: .medium)
        nameLabel.textColor = NSColor.labelColor
        nameLabel.alignment = .center
        nameLabel.isEditable = false
        nameLabel.isBordered = false
        nameLabel.backgroundColor = NSColor.clear
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(nameLabel)

        // Icon (moved to center)
        iconImageView = NSImageView()
        iconImageView.imageScaling = .scaleProportionallyUpOrDown
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(iconImageView)

        // Shortcut (at bottom)
        shortcutLabel = NSTextField(labelWithString: "")
        shortcutLabel.font = NSFont.systemFont(ofSize: 10)
        shortcutLabel.textColor = NSColor.secondaryLabelColor
        shortcutLabel.alignment = .center
        shortcutLabel.isEditable = false
        shortcutLabel.isBordered = false
        shortcutLabel.backgroundColor = NSColor.clear
        shortcutLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(shortcutLabel)

        // Auto Layout constraints
        NSLayoutConstraint.activate([
            // Container view fills the entire cell
            containerView.topAnchor.constraint(equalTo: view.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: view.bottomAnchor),

            // App name at the top
            nameLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
            nameLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 4),
            nameLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -4),
            nameLabel.heightAnchor.constraint(equalToConstant: 16),

            // Icon in the center
            iconImageView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            iconImageView.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 8),
            iconImageView.widthAnchor.constraint(equalToConstant: 48),
            iconImageView.heightAnchor.constraint(equalToConstant: 48),

            // Shortcut at the bottom
            shortcutLabel.topAnchor.constraint(equalTo: iconImageView.bottomAnchor, constant: 8),
            shortcutLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 4),
            shortcutLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -4),
            shortcutLabel.bottomAnchor.constraint(lessThanOrEqualTo: containerView.bottomAnchor, constant: -8),
            shortcutLabel.heightAnchor.constraint(equalToConstant: 14)
        ])
    }

    func configure(with app: AppModel) {
        iconImageView.image = app.icon
        nameLabel.stringValue = app.appDisplayName

        // Format shortcut display
        if let shortcut = app.shortcut {
            shortcutLabel.stringValue = shortcut.description
        } else {
            shortcutLabel.stringValue = "No shortcut"
        }
    }

    override var isSelected: Bool {
        didSet {
            updateSelection()
        }
    }

    private func updateSelection() {
        if isSelected {
            containerView.layer?.backgroundColor = NSColor.selectedControlColor.withAlphaComponent(0.6).cgColor
            containerView.layer?.borderWidth = 2
            containerView.layer?.borderColor = NSColor.selectedControlColor.cgColor
        } else {
            containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.withAlphaComponent(0.3).cgColor
            containerView.layer?.borderWidth = 0
        }
    }
}
