//
//  SettingsViewController.swift
//  Switchman
//
//  Created by <PERSON><PERSON><PERSON> on 4/18/16.
//  Copyright © 2016 <PERSON><PERSON><PERSON>. All rights reserved.
//

import Cocoa
import LaunchAtLogin

class SettingsViewController: NSViewController {

    @IBOutlet weak var btnLaunchAtLogin: NSButton!
    @IBOutlet weak var btnEnableShortcut: NSButton!
    @IBOutlet weak var btnEnableMenuBarIcon: NSButton!
    @IBOutlet weak var btnEnableMenuBarIconShowHideKey: NSButton!
    @IBOutlet weak var btnEnableDeactivateKey: NSButton!
    @IBOutlet weak var btnShortcutDeactivateKey: NSPopUpButton!
    @IBOutlet weak var slider: NSSlider!

    override func viewDidLoad() {
        super.viewDidLoad()

        view.layer?.backgroundColor = NSColor.clear.cgColor

        btnLaunchAtLogin.state = LaunchAtLogin.isEnabled ? .on : .off

        btnEnableShortcut.state = defaults[.EnableShortcut] ? .on : .off

        btnEnableMenuBarIcon.state = defaults[.enableMenuBarIcon] ? .on : .off
        btnEnableMenuBarIconShowHideKey.state = defaults[.enableMenuBarIconShowHideKey] ? .on : .off

        let isEnableDeactivateKey = defaults[.EnableDeactivateKey]

        btnEnableDeactivateKey.state = isEnableDeactivateKey ? .on : .off

        btnShortcutDeactivateKey.selectItem(at: defaults[.DeactivateKey])
        btnShortcutDeactivateKey.isEnabled = isEnableDeactivateKey

        slider.doubleValue = defaults[.DelayInterval]
        slider.isEnabled = isEnableDeactivateKey

        NotificationCenter.default.addObserver(self,
                                               selector: #selector(updateMenuBarToggleState),
                                               name: .updateMenuBarToggleState,
                                               object: nil)
    }

    @IBAction func toggleLaunchAtLogin(_ sender: Any) {
        LaunchAtLogin.isEnabled = !LaunchAtLogin.isEnabled
    }

    @IBAction func toggleEnableShortcut(_ sender: Any) {
        let enable = btnEnableShortcut.state == .on

        defaults[.EnableShortcut] = enable

        if enable {
            ShortcutMonitor.register()
        } else {
            ShortcutMonitor.unregister()
        }
    }

    @IBAction func toggleEnableMenuBarIcon(_ sender: Any) {
        let enable = btnEnableMenuBarIcon.state == .on

        defaults[.enableMenuBarIcon] = enable

        if enable {
            sharedAppDelegate?.statusItemController.showInMenuBar()
        } else {
            sharedAppDelegate?.statusItemController.hideInMenuBar()
        }
    }

    @IBAction func toggleEnableMenuBarIconShowHideKey(_ sender: Any) {
        let enable = btnEnableMenuBarIconShowHideKey.state == .on

        defaults[.enableMenuBarIconShowHideKey] = enable

        if enable {
            sharedAppDelegate?.registerMenubarIconShortcut()
        } else {
            sharedAppDelegate?.unregisterMenubarIconShortcut()
        }
    }

    @IBAction func toggleEnableDeactivateKey(_ sender: Any) {
        let isEnableDeactivateKey = btnEnableDeactivateKey.state == .on

        defaults[.EnableDeactivateKey] = isEnableDeactivateKey

        btnShortcutDeactivateKey.isEnabled = isEnableDeactivateKey
        slider.isEnabled = isEnableDeactivateKey
    }

    @IBAction func changeDeactivateKey(_ sender: Any) {
        defaults[.DeactivateKey] = btnShortcutDeactivateKey.indexOfSelectedItem
    }

    @IBAction func changeShortcutReactivateInterval(_ sender: Any) {
        defaults[.DelayInterval] = slider.doubleValue
    }

    @IBAction func exit(_ sender: Any) {
        NSApp.terminate(self)
    }

    @objc func updateMenuBarToggleState() {
        btnEnableMenuBarIcon.state = defaults[.enableMenuBarIcon] ? .on : .off
    }

}
