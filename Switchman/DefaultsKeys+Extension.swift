//
//  DefaultsKeys+Extension.swift
//  Switchman
//
//  Created by <PERSON><PERSON><PERSON> on 4/18/16.
//  Copyright © 2016 Alvin<PERSON><PERSON>. All rights reserved.
//

import Foundation

extension DefaultsKeys {

    static let ModifyKey      = DefaultsKey<Int>("ModifyKey")
    static let EnableShortcut = DefaultsKey<Bool>("EnableShortcut")
    static let DelayInterval  = DefaultsKey<Double>("DelayInterval")
    static let Shortcuts      = DefaultsKey<[Data]?>("Shortcuts")
    static let DeactivateKey  = DefaultsKey<Int>("DeactivateKey")
    static let EnableDeactivateKey = DefaultsKey<Bool>("EnableDeactivateKey")
    static let LaunchAtLoginKey = DefaultsKey<Bool>("LaunchAtLoginKey")
    static let enableMenuBarIcon = DefaultsKey<Bool>("enableMenuBarIcon")
    static let enableMenuBarIconShowHideKey = DefaultsKey<Bool>("enableMenuBarIconShowHideKey")
    static let enableAppSwitcher = DefaultsKey<Bool>("enableAppSwitcher")
    static let appSwitcherFirstTimeSetup = DefaultsKey<Bool>("AppSwitcherFirstTimeSetup")
    static let appSwitcherLayoutMode = DefaultsKey<Int>("appSwitcherLayoutMode") // 0: single row, 1: multi row

}
